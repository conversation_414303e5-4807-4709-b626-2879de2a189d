package com.rs.module.ihc.service.dch.handler;

import com.rs.module.base.service.dch.context.DataChangeEventContext;
import com.rs.module.base.service.dch.enums.DataChangeEventTypeEnum;
import com.rs.module.base.service.dch.handler.AbstractDataChangeEventHandler;
import com.rs.module.base.service.dch.handler.DataChangeEventHandlerResult;
import com.rs.module.ihc.entity.ipm.VisitDO;
import com.rs.module.ihc.service.ipm.VisitService;
import com.rs.module.ihc.service.mr.MedicalRecordService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 半年体检
 *
 * <AUTHOR>
 * @date 2025/01/05
 */
@Slf4j
@Component
@AllArgsConstructor
public class VisitHandler extends AbstractDataChangeEventHandler {
    public final MedicalRecordService medicalRecordService;
    public final VisitService visitService;

    @Override
    public String getSupportedBusinessType() {
        return VisitHandler.class.getSimpleName();
    }

    @Override
    public int getPriority() {
        return 1;
    }

    @Override
    protected List<String> getSupportedTableNames() {
        return Arrays.asList(
                "ihc_ipm_visit"
        );
    }

    @Override
    protected Set<DataChangeEventTypeEnum> getSupportedEventTypes() {
        return new HashSet<>(Arrays.asList(
                DataChangeEventTypeEnum.INSERT
                , DataChangeEventTypeEnum.UPDATE
                , DataChangeEventTypeEnum.DELETE
        ));
    }

    @Override
    protected DataChangeEventHandlerResult doHandle(DataChangeEventContext context) {
        switch (context.getEventType()) {
            case INSERT:
                return handleInsert(context);
            case UPDATE:
                return handleUpdate(context);
            case DELETE:
                return handleDelete(context);
        }
        return DataChangeEventHandlerResult.success();
    }
    /**
     * 处理插入事件
     */
    private DataChangeEventHandlerResult handleInsert(DataChangeEventContext context) {

        gettVisit();
        return DataChangeEventHandlerResult.success("处理成功");
    }
    /**
     * 处理更新事件
     */
    private DataChangeEventHandlerResult handleUpdate(DataChangeEventContext context){
        return DataChangeEventHandlerResult.success("处理成功");
    }
    /**
     * 处理删除事件
     */
    private DataChangeEventHandlerResult handleDelete(DataChangeEventContext context){
        return DataChangeEventHandlerResult.success("处理成功");
    }
    public VisitDO gettVisit(String id) {
        return visitService.getVisit(id);
    }

}
