package com.rs.module.ihc.service.dch.handler;

import com.rs.module.base.service.dch.context.DataChangeEventContext;
import com.rs.module.base.service.dch.enums.DataChangeEventTypeEnum;
import com.rs.module.base.service.dch.handler.AbstractDataChangeEventHandler;
import com.rs.module.base.service.dch.handler.DataChangeEventHandlerResult;
import com.rs.module.ihc.constant.IhcsHealthCheckupConstant;
import com.rs.module.ihc.entity.hc.HealthCheckupDO;
import com.rs.module.ihc.service.hc.HealthCheckupService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 半年体检
 *
 * <AUTHOR>
 * @date 2025/01/05
 */
@Slf4j
@Component
@AllArgsConstructor
public class BntjHandler extends AbstractDataChangeEventHandler {
    public final HealthCheckupService healthCheckupService;

    @Override
    public String getSupportedBusinessType() {
        return BntjHandler.class.getSimpleName();
    }

    @Override
    public int getPriority() {
        return 1;
    }

    @Override
    protected List<String> getSupportedTableNames() {
        return Arrays.asList(
                "acp_pm_prisoner_kss_in"
        );
    }

    @Override
    protected Set<DataChangeEventTypeEnum> getSupportedEventTypes() {
        return new HashSet<>(Arrays.asList(
                DataChangeEventTypeEnum.INSERT
        ));
    }

    @Override
    protected DataChangeEventHandlerResult doHandle(DataChangeEventContext context) {
        HealthCheckupDO checkupDO = HealthCheckupDO.builder()
                .jgrybm(context.getNewDataMap().get("jgrybm").toString())
                .checkupStatus(IhcsHealthCheckupConstant.CHECKUP_STATUS_DTJ)
                .build();
        healthCheckupService.save(checkupDO);
        return DataChangeEventHandlerResult.success();
    }

}
