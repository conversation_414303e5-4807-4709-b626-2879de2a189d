# PostgreSQL 数据变更事件触发器配置说明

## 概述

本文档说明如何在 PostgreSQL 数据库中配置数据变更事件触发器，用于自动捕获表的 INSERT、UPDATE、DELETE 操作并记录到事件日志表中。

## 表结构

### 主表：acp_pm_data_change_event_log
存储所有数据变更事件的日志信息。

### 配置表：acp_pm_data_change_business_config
存储业务类型配置信息（可选）。

## 触发器函数

### 1. 通用触发器函数（推荐）

**函数名**: `trigger_data_change_event_generic`

**参数**:
- `business_type_param`: 业务类型（默认：'DEFAULT'）
- `primary_key_field`: 主键字段名（默认：'id'）

**特点**:
- 支持任意表结构
- 自动将整行数据转换为JSON格式
- 支持自定义业务类型和主键字段
- 通用性强，维护成本低

**使用示例**:
```sql
-- 为监管人员表创建触发器
CREATE TRIGGER tr_pam_pm_prisoner_data_change
    AFTER INSERT OR UPDATE OR DELETE ON pam_pm_prisoner
    FOR EACH ROW EXECUTE FUNCTION trigger_data_change_event_generic('PRISONER', 'id');

-- 为案件表创建触发器
CREATE TRIGGER tr_pam_pm_case_data_change
    AFTER INSERT OR UPDATE OR DELETE ON pam_pm_case
    FOR EACH ROW EXECUTE FUNCTION trigger_data_change_event_generic('CASE', 'case_id');
```

### 2. 专用触发器函数

**函数名**: `trigger_prisoner_data_change_event`

**特点**:
- 针对特定业务场景优化
- 只记录关键字段，减少存储空间
- 可以进行特殊的数据处理
- 适用于对性能要求较高的场景

**使用示例**:
```sql
-- 使用专用触发器函数
CREATE TRIGGER tr_pam_pm_prisoner_data_change_custom
    AFTER INSERT OR UPDATE OR DELETE ON pam_pm_prisoner
    FOR EACH ROW EXECUTE FUNCTION trigger_prisoner_data_change_event();
```

## 触发器创建步骤

### 1. 执行建表脚本
```bash
psql -U username -d database_name -f acp_pm_data_change_event_log.sql
```

### 2. 为业务表创建触发器

**方式一：使用通用触发器（推荐）**
```sql
-- 语法：
CREATE TRIGGER trigger_name
    AFTER INSERT OR UPDATE OR DELETE ON table_name
    FOR EACH ROW EXECUTE FUNCTION trigger_data_change_event_generic('BUSINESS_TYPE', 'primary_key_field');

-- 示例：
CREATE TRIGGER tr_pam_pm_prisoner_data_change
    AFTER INSERT OR UPDATE OR DELETE ON pam_pm_prisoner
    FOR EACH ROW EXECUTE FUNCTION trigger_data_change_event_generic('PRISONER', 'id');
```

**方式二：使用专用触发器**
```sql
-- 需要先创建专用的触发器函数，然后创建触发器
CREATE TRIGGER tr_table_name_data_change
    AFTER INSERT OR UPDATE OR DELETE ON table_name
    FOR EACH ROW EXECUTE FUNCTION your_custom_trigger_function();
```

### 3. 验证触发器

```sql
-- 查看表的触发器
SELECT 
    trigger_name,
    event_manipulation,
    action_statement,
    action_timing
FROM information_schema.triggers 
WHERE event_object_table = 'your_table_name';

-- 测试触发器
INSERT INTO pam_pm_prisoner (id, jgrybm, xm, jsh) VALUES ('test001', 'P001', '测试人员', 'R001');

-- 查看生成的事件日志
SELECT * FROM acp_pm_data_change_event_log WHERE primary_key_id = 'test001';
```

## 业务类型配置

### 支持的业务类型

| 业务类型 | 描述 | 相关表 |
|---------|------|--------|
| PRISONER | 监管人员 | pam_pm_prisoner, pam_pm_prisoner_room, pam_pm_prisoner_case |
| CASE | 案件信息 | pam_pm_case, pam_pm_case_detail |
| ROOM | 监室信息 | pam_pm_room, pam_pm_room_config |

### 添加新的业务类型

1. **在配置表中添加记录**:
```sql
INSERT INTO acp_pm_data_change_business_config 
(id, business_type, business_name, description, enabled, supported_tables, handler_class, priority, max_retry_count) 
VALUES 
('new_business_config', 'NEW_BUSINESS', '新业务', '新业务相关数据变更处理', 1, 
 '["new_table1", "new_table2"]', 
 'com.rs.module.base.service.dch.handler.impl.NewBusinessDataChangeHandler', 20, 3);
```

2. **为相关表创建触发器**:
```sql
CREATE TRIGGER tr_new_table1_data_change
    AFTER INSERT OR UPDATE OR DELETE ON new_table1
    FOR EACH ROW EXECUTE FUNCTION trigger_data_change_event_generic('NEW_BUSINESS', 'id');
```

## 性能优化

### 1. 索引优化

已创建的索引：
- 业务类型索引
- 状态索引
- 时间索引
- 复合索引

### 2. 分区表（可选）

对于大数据量场景，可以考虑按时间分区：

```sql
-- 将主表改为分区表
ALTER TABLE acp_pm_data_change_event_log 
PARTITION BY RANGE (event_time);

-- 创建月度分区
CREATE TABLE acp_pm_data_change_event_log_y2025m01 
PARTITION OF acp_pm_data_change_event_log
FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');

CREATE TABLE acp_pm_data_change_event_log_y2025m02 
PARTITION OF acp_pm_data_change_event_log
FOR VALUES FROM ('2025-02-01') TO ('2025-03-01');
```

### 3. 触发器性能优化

- 只记录必要的字段
- 避免在触发器中执行复杂逻辑
- 考虑使用异步处理

## 监控和维护

### 1. 监控SQL

```sql
-- 查看事件统计
SELECT * FROM get_data_change_event_stats();

-- 查看待处理事件数量
SELECT COUNT(*) FROM acp_pm_data_change_event_log WHERE status = 'PENDING' AND is_del = 0;

-- 查看失败事件
SELECT business_type, table_name, COUNT(*) as failed_count
FROM acp_pm_data_change_event_log 
WHERE status = 'FAILED' AND is_del = 0
GROUP BY business_type, table_name;
```

### 2. 清理过期数据

```sql
-- 手动清理过期数据（保留7天）
SELECT cleanup_expired_data_change_events(7);

-- 查看清理结果
SELECT COUNT(*) as deleted_count 
FROM acp_pm_data_change_event_log 
WHERE status = 'SUCCESS' AND is_del = 1;
```

### 3. 性能监控

```sql
-- 查看表大小
SELECT 
    schemaname,
    tablename,
    attname,
    n_distinct,
    correlation
FROM pg_stats 
WHERE tablename = 'acp_pm_data_change_event_log';

-- 查看索引使用情况
SELECT 
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE relname = 'acp_pm_data_change_event_log';
```

## 故障排查

### 1. 触发器未生效

**检查步骤**:
```sql
-- 检查触发器是否存在
SELECT * FROM information_schema.triggers 
WHERE event_object_table = 'your_table_name';

-- 检查触发器函数是否存在
SELECT * FROM information_schema.routines 
WHERE routine_name = 'trigger_data_change_event_generic';
```

### 2. 事件未记录

**可能原因**:
- 触发器未正确创建
- 触发器函数有语法错误
- 权限不足
- 表结构不匹配

**排查方法**:
```sql
-- 查看PostgreSQL日志
-- 手动执行触发器函数测试
-- 检查表权限
```

### 3. 性能问题

**优化建议**:
- 定期清理过期数据
- 优化索引结构
- 考虑使用分区表
- 监控触发器执行时间

## 注意事项

1. **权限要求**: 执行用户需要有创建触发器和函数的权限
2. **表结构依赖**: 触发器函数中的字段名需要与实际表结构匹配
3. **性能影响**: 触发器会影响DML操作性能，需要合理设计
4. **事务一致性**: 触发器在同一事务中执行，失败会回滚主操作
5. **JSON格式**: 确保生成的JSON格式正确，避免特殊字符问题

## 最佳实践

1. **使用通用触发器**: 优先使用 `trigger_data_change_event_generic` 函数
2. **合理命名**: 触发器名称要清晰明确
3. **文档记录**: 记录每个触发器的用途和配置
4. **定期维护**: 定期检查触发器状态和性能
5. **测试验证**: 在测试环境充分验证后再部署到生产环境

## 扩展功能

### 1. 条件触发

可以修改触发器函数，添加条件判断：

```sql
-- 只在特定条件下触发
IF NEW.status != OLD.status THEN
    -- 记录状态变更事件
END IF;
```

### 2. 批量处理

对于高频变更的表，可以考虑批量处理：

```sql
-- 创建临时表缓存事件
-- 定期批量插入到日志表
```

### 3. 异步处理

使用 PostgreSQL 的 NOTIFY/LISTEN 机制实现异步处理：

```sql
-- 在触发器中发送通知
PERFORM pg_notify('data_change_event', event_id);
```
