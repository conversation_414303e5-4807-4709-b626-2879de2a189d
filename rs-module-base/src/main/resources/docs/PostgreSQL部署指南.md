# PostgreSQL 数据变更事件处理系统部署指南

## 快速开始

### 1. 环境要求

- PostgreSQL 12+ 
- 具有创建表、函数、触发器的权限
- 支持 UUID 扩展（通常默认已安装）

### 2. 一键部署

```bash
# 连接到PostgreSQL数据库
psql -U your_username -d your_database

# 执行部署脚本
\i rs-module-base/src/main/resources/sql/postgresql/deploy.sql
```

### 3. 验证部署

```bash
# 执行测试脚本
\i rs-module-base/src/main/resources/sql/postgresql/test.sql
```

## 详细部署步骤

### 步骤1：创建数据库表

```sql
-- 执行完整的建表脚本
\i acp_pm_data_change_event_log.sql

-- 或者执行简化版
\i acp_pm_data_change_event_log_simple.sql
```

### 步骤2：为业务表创建触发器

**通用方式（推荐）**：
```sql
-- 语法
CREATE TRIGGER trigger_name
    AFTER INSERT OR UPDATE OR DELETE ON table_name
    FOR EACH ROW EXECUTE FUNCTION trigger_data_change_event_generic('BUSINESS_TYPE', 'primary_key_field');

-- 示例：监管人员表
CREATE TRIGGER tr_pam_pm_prisoner_data_change
    AFTER INSERT OR UPDATE OR DELETE ON pam_pm_prisoner
    FOR EACH ROW EXECUTE FUNCTION trigger_data_change_event_generic('PRISONER', 'id');

-- 示例：案件表
CREATE TRIGGER tr_pam_pm_case_data_change
    AFTER INSERT OR UPDATE OR DELETE ON pam_pm_case
    FOR EACH ROW EXECUTE FUNCTION trigger_data_change_event_generic('CASE', 'id');
```

### 步骤3：配置应用程序

在 `application.yml` 中添加配置：

```yaml
# 数据库配置（使用PostgreSQL）
spring:
  datasource:
    driver-class-name: org.postgresql.Driver
    url: **********************************************
    username: your_username
    password: your_password

# 数据变更事件配置
rs:
  data-change-event:
    enabled: true
    async-enabled: true
    batch-size: 100
```

### 步骤4：配置 MyBatis

如果使用 PostgreSQL，需要使用对应的 Mapper 文件：

```xml
<!-- 在 mybatis-config.xml 或 application.yml 中配置 -->
mybatis:
  mapper-locations: 
    - classpath:mapper/postgresql/*.xml
```

## 业务表触发器配置

### 监管人员相关表

```sql
-- 监管人员基础信息表
CREATE TRIGGER tr_pam_pm_prisoner_data_change
    AFTER INSERT OR UPDATE OR DELETE ON pam_pm_prisoner
    FOR EACH ROW EXECUTE FUNCTION trigger_data_change_event_generic('PRISONER', 'id');

-- 监管人员监室信息表
CREATE TRIGGER tr_pam_pm_prisoner_room_data_change
    AFTER INSERT OR UPDATE OR DELETE ON pam_pm_prisoner_room
    FOR EACH ROW EXECUTE FUNCTION trigger_data_change_event_generic('PRISONER', 'id');

-- 监管人员案件信息表
CREATE TRIGGER tr_pam_pm_prisoner_case_data_change
    AFTER INSERT OR UPDATE OR DELETE ON pam_pm_prisoner_case
    FOR EACH ROW EXECUTE FUNCTION trigger_data_change_event_generic('PRISONER', 'id');
```

### 案件相关表

```sql
-- 案件基础信息表
CREATE TRIGGER tr_pam_pm_case_data_change
    AFTER INSERT OR UPDATE OR DELETE ON pam_pm_case
    FOR EACH ROW EXECUTE FUNCTION trigger_data_change_event_generic('CASE', 'id');

-- 案件详情表
CREATE TRIGGER tr_pam_pm_case_detail_data_change
    AFTER INSERT OR UPDATE OR DELETE ON pam_pm_case_detail
    FOR EACH ROW EXECUTE FUNCTION trigger_data_change_event_generic('CASE', 'id');
```

### 监室相关表

```sql
-- 监室基础信息表
CREATE TRIGGER tr_pam_pm_room_data_change
    AFTER INSERT OR UPDATE OR DELETE ON pam_pm_room
    FOR EACH ROW EXECUTE FUNCTION trigger_data_change_event_generic('ROOM', 'id');

-- 监室配置表
CREATE TRIGGER tr_pam_pm_room_config_data_change
    AFTER INSERT OR UPDATE OR DELETE ON pam_pm_room_config
    FOR EACH ROW EXECUTE FUNCTION trigger_data_change_event_generic('ROOM', 'id');
```

## 管理和维护

### 1. 查看系统状态

```sql
-- 查看事件统计
SELECT * FROM get_data_change_event_stats();

-- 查看待处理事件
SELECT business_type, COUNT(*) as pending_count
FROM acp_pm_data_change_event_log 
WHERE status = 'PENDING' AND is_del = 0
GROUP BY business_type;

-- 查看失败事件
SELECT business_type, table_name, error_message, COUNT(*) as failed_count
FROM acp_pm_data_change_event_log 
WHERE status = 'FAILED' AND is_del = 0
GROUP BY business_type, table_name, error_message;
```

### 2. 手动清理数据

```sql
-- 清理7天前的成功事件
SELECT cleanup_expired_data_change_events(7);

-- 清理30天前的成功事件
SELECT cleanup_expired_data_change_events(30);

-- 查看清理结果
SELECT 
    status,
    is_del,
    COUNT(*) as count,
    MIN(event_time) as earliest,
    MAX(event_time) as latest
FROM acp_pm_data_change_event_log 
GROUP BY status, is_del;
```

### 3. 性能监控

```sql
-- 查看表大小
SELECT 
    schemaname,
    tablename,
    attname,
    n_distinct,
    correlation
FROM pg_stats 
WHERE tablename = 'acp_pm_data_change_event_log';

-- 查看索引使用情况
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE relname = 'acp_pm_data_change_event_log';

-- 查看表统计信息
SELECT 
    relname,
    n_tup_ins,
    n_tup_upd,
    n_tup_del,
    n_live_tup,
    n_dead_tup
FROM pg_stat_user_tables 
WHERE relname = 'acp_pm_data_change_event_log';
```

## 故障排查

### 1. 触发器不工作

```sql
-- 检查触发器是否存在
SELECT 
    trigger_name,
    event_object_table,
    action_statement,
    action_timing,
    event_manipulation
FROM information_schema.triggers 
WHERE event_object_table = 'your_table_name';

-- 检查触发器函数是否存在
SELECT routine_name, routine_definition
FROM information_schema.routines 
WHERE routine_name = 'trigger_data_change_event_generic';
```

### 2. 事件未记录

**可能原因**：
- 触发器未正确创建
- 主键字段名不匹配
- 权限不足
- 函数执行异常

**排查方法**：
```sql
-- 查看PostgreSQL错误日志
-- 手动测试触发器函数
SELECT trigger_data_change_event_generic('TEST', 'id');

-- 检查表权限
SELECT grantee, privilege_type 
FROM information_schema.table_privileges 
WHERE table_name = 'acp_pm_data_change_event_log';
```

### 3. 性能问题

**优化建议**：
- 定期执行 VACUUM 和 ANALYZE
- 监控触发器执行时间
- 考虑使用分区表
- 优化 JSON 数据大小

```sql
-- 手动维护
VACUUM ANALYZE acp_pm_data_change_event_log;

-- 重建索引
REINDEX TABLE acp_pm_data_change_event_log;
```

## 高级配置

### 1. 分区表配置

```sql
-- 创建按月分区的表
CREATE TABLE acp_pm_data_change_event_log_partitioned (
    LIKE acp_pm_data_change_event_log INCLUDING ALL
) PARTITION BY RANGE (event_time);

-- 创建分区
CREATE TABLE acp_pm_data_change_event_log_y2025m01 
PARTITION OF acp_pm_data_change_event_log_partitioned
FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');
```

### 2. 异步处理配置

```sql
-- 使用NOTIFY/LISTEN实现异步处理
CREATE OR REPLACE FUNCTION trigger_data_change_event_async()
RETURNS TRIGGER AS $$
BEGIN
    -- 记录事件
    -- ... 事件记录逻辑 ...
    
    -- 发送异步通知
    PERFORM pg_notify('data_change_event', NEW.id);
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

### 3. 条件触发

```sql
-- 只在特定条件下触发
CREATE OR REPLACE FUNCTION trigger_data_change_event_conditional()
RETURNS TRIGGER AS $$
BEGIN
    -- 只在状态字段变更时触发
    IF TG_OP = 'UPDATE' AND OLD.status = NEW.status THEN
        RETURN NEW;
    END IF;
    
    -- 执行正常的事件记录逻辑
    -- ...
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

## 注意事项

1. **UUID扩展**：确保数据库已安装 `uuid-ossp` 或 `pgcrypto` 扩展
2. **权限管理**：确保应用用户有足够的权限
3. **事务处理**：触发器在同一事务中执行，异常会导致回滚
4. **JSON处理**：PostgreSQL 对 JSON 支持较好，但要注意特殊字符
5. **性能监控**：定期监控触发器对业务操作的性能影响

## 卸载

如需卸载系统：

```sql
-- 删除所有相关触发器
DROP TRIGGER IF EXISTS tr_pam_pm_prisoner_data_change ON pam_pm_prisoner;
-- ... 删除其他触发器 ...

-- 删除触发器函数
DROP FUNCTION IF EXISTS trigger_data_change_event_generic;
DROP FUNCTION IF EXISTS cleanup_expired_data_change_events;
DROP FUNCTION IF EXISTS get_data_change_event_stats;
DROP FUNCTION IF EXISTS update_modified_column;

-- 删除表
DROP TABLE IF EXISTS acp_pm_data_change_event_log;
DROP TABLE IF EXISTS acp_pm_data_change_business_config;
```
