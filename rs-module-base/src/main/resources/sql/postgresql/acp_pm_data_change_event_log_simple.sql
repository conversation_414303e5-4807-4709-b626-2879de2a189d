-- PostgreSQL 数据变更事件日志表 - 简化版建表语句

-- 1. 创建主表
CREATE TABLE acp_pm_data_change_event_log (
    id VARCHAR(32) NOT NULL PRIMARY KEY,
    event_type VARCHAR(20) NOT NULL,
    table_name VARCHAR(100) NOT NULL,
    business_type VARCHAR(50) NOT NULL,
    primary_key_id VARCHAR(100) NOT NULL,
    old_data TEXT,
    new_data TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    handler_class VARCHAR(200),
    retry_count INTEGER NOT NULL DEFAULT 0,
    max_retry_count INTEGER NOT NULL DEFAULT 3,
    error_message TEXT,
    event_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    process_start_time TIMESTAMP,
    process_end_time TIMESTAMP,
    next_retry_time TIMESTAMP,
    extend_info TEXT,
    is_del SMALLINT NOT NULL DEFAULT 0,
    add_user VARCHAR(32),
    add_user_name VARCHAR(100),
    add_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_user VARCHAR(32),
    update_user_name VARCHAR(100),
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 2. 创建索引
CREATE INDEX idx_acp_data_change_status ON acp_pm_data_change_event_log (status);
CREATE INDEX idx_acp_data_change_business_type ON acp_pm_data_change_event_log (business_type);
CREATE INDEX idx_acp_data_change_event_time ON acp_pm_data_change_event_log (event_time);
CREATE INDEX idx_acp_data_change_composite ON acp_pm_data_change_event_log (business_type, status, event_time);

-- 3. 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.update_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_acp_pm_data_change_event_log_update_time
    BEFORE UPDATE ON acp_pm_data_change_event_log
    FOR EACH ROW EXECUTE FUNCTION update_modified_column();

-- 4. 创建通用数据变更触发器函数
CREATE OR REPLACE FUNCTION trigger_data_change_event_generic(
    business_type_param VARCHAR(50) DEFAULT 'DEFAULT',
    primary_key_field VARCHAR(50) DEFAULT 'id'
)
RETURNS TRIGGER AS $$
DECLARE
    event_id VARCHAR(32);
    old_data_json TEXT;
    new_data_json TEXT;
    pk_value TEXT;
BEGIN
    -- 生成UUID（去掉横线）
    event_id := REPLACE(gen_random_uuid()::TEXT, '-', '');

    -- 根据操作类型处理
    IF TG_OP = 'INSERT' THEN
        -- 获取主键值
        EXECUTE format('SELECT ($1).%I::TEXT', primary_key_field) INTO pk_value USING NEW;

        -- 构建新数据JSON
        new_data_json := row_to_json(NEW)::TEXT;

        INSERT INTO acp_pm_data_change_event_log
        (id, event_type, table_name, business_type, primary_key_id, new_data, event_time)
        VALUES
        (event_id, 'INSERT', TG_TABLE_NAME, business_type_param, pk_value, new_data_json, CURRENT_TIMESTAMP);

        RETURN NEW;

    ELSIF TG_OP = 'UPDATE' THEN
        -- 获取主键值
        EXECUTE format('SELECT ($1).%I::TEXT', primary_key_field) INTO pk_value USING NEW;

        -- 构建数据JSON
        old_data_json := row_to_json(OLD)::TEXT;
        new_data_json := row_to_json(NEW)::TEXT;

        INSERT INTO acp_pm_data_change_event_log
        (id, event_type, table_name, business_type, primary_key_id, old_data, new_data, event_time)
        VALUES
        (event_id, 'UPDATE', TG_TABLE_NAME, business_type_param, pk_value, old_data_json, new_data_json, CURRENT_TIMESTAMP);

        RETURN NEW;

    ELSIF TG_OP = 'DELETE' THEN
        -- 获取主键值
        EXECUTE format('SELECT ($1).%I::TEXT', primary_key_field) INTO pk_value USING OLD;

        -- 构建旧数据JSON
        old_data_json := row_to_json(OLD)::TEXT;

        INSERT INTO acp_pm_data_change_event_log
        (id, event_type, table_name, business_type, primary_key_id, old_data, event_time)
        VALUES
        (event_id, 'DELETE', TG_TABLE_NAME, business_type_param, pk_value, old_data_json, CURRENT_TIMESTAMP);

        RETURN OLD;
    END IF;

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 5. 示例：为监管人员表创建触发器
-- 注意：请根据实际的表名和字段调整

-- 假设监管人员表名为 pam_pm_prisoner，主键为 id
CREATE TRIGGER tr_pam_pm_prisoner_data_change
    AFTER INSERT OR UPDATE OR DELETE ON pam_pm_prisoner
    FOR EACH ROW EXECUTE FUNCTION trigger_data_change_event_generic('PRISONER', 'id');

-- 6. 验证安装
-- 执行以下SQL验证表和触发器是否正确创建：

-- 检查表是否存在
SELECT tablename FROM pg_tables WHERE tablename = 'acp_pm_data_change_event_log';

-- 检查触发器函数是否存在
SELECT routine_name FROM information_schema.routines
WHERE routine_name = 'trigger_data_change_event_generic';

-- 检查索引是否存在
SELECT indexname FROM pg_indexes
WHERE tablename = 'acp_pm_data_change_event_log';

-- 7. 测试数据（可选）
-- 插入测试配置
INSERT INTO acp_pm_data_change_business_config
(id, business_type, business_name, description, enabled, supported_tables, handler_class, priority, max_retry_count)
VALUES
('test_config', 'TEST', '测试业务', '测试业务数据变更处理', 1,
 '["test_table"]',
 'com.rs.module.base.service.dch.handler.impl.TestDataChangeHandler', 50, 3);

-- 插入测试事件
INSERT INTO acp_pm_data_change_event_log
(id, event_type, table_name, business_type, primary_key_id, new_data, event_time)
VALUES
('test001', 'INSERT', 'test_table', 'TEST', 'test_pk_001', '{"name":"test","value":"123"}', CURRENT_TIMESTAMP);

-- 查询测试数据
SELECT * FROM acp_pm_data_change_event_log WHERE id = 'test001';
