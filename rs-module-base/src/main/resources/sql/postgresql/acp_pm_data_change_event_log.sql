-- PostgreSQL 数据变更事件日志表
CREATE TABLE acp_pm_data_change_event_log (
    id VARCHAR(32) NOT NULL,
    event_type VARCHAR(20) NOT NULL,
    table_name VARCHAR(100) NOT NULL,
    business_type VARCHAR(50) NOT NULL,
    primary_key_id VARCHAR(100) NOT NULL,
    old_data TEXT,
    new_data TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    handler_class VARCHAR(200),
    retry_count INTEGER NOT NULL DEFAULT 0,
    max_retry_count INTEGER NOT NULL DEFAULT 3,
    error_message TEXT,
    event_time TIMESTAMP NOT NULL,
    process_start_time TIMESTAMP,
    process_end_time TIMESTAMP,
    next_retry_time TIMESTAMP,
    extend_info TEXT,
    is_del SMALLINT NOT NULL DEFAULT 0,
    add_user VARCHAR(32),
    add_user_name VARCHAR(100),
    add_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_user VARCHAR(32),
    update_user_name VARCHAR(100),
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT pk_acp_pm_data_change_event_log PRIMARY KEY (id)
);

-- 添加表注释
COMMENT ON TABLE acp_pm_data_change_event_log IS '数据变更事件日志表';

-- 添加字段注释
COMMENT ON COLUMN acp_pm_data_change_event_log.id IS '主键ID';
COMMENT ON COLUMN acp_pm_data_change_event_log.event_type IS '事件类型（INSERT/UPDATE/DELETE）';
COMMENT ON COLUMN acp_pm_data_change_event_log.table_name IS '业务表名';
COMMENT ON COLUMN acp_pm_data_change_event_log.business_type IS '业务类型';
COMMENT ON COLUMN acp_pm_data_change_event_log.primary_key_id IS '主键ID';
COMMENT ON COLUMN acp_pm_data_change_event_log.old_data IS '变更前数据（JSON格式）';
COMMENT ON COLUMN acp_pm_data_change_event_log.new_data IS '变更后数据（JSON格式）';
COMMENT ON COLUMN acp_pm_data_change_event_log.status IS '事件状态（PENDING/PROCESSING/SUCCESS/FAILED/SKIPPED）';
COMMENT ON COLUMN acp_pm_data_change_event_log.handler_class IS '处理器类名';
COMMENT ON COLUMN acp_pm_data_change_event_log.retry_count IS '重试次数';
COMMENT ON COLUMN acp_pm_data_change_event_log.max_retry_count IS '最大重试次数';
COMMENT ON COLUMN acp_pm_data_change_event_log.error_message IS '错误信息';
COMMENT ON COLUMN acp_pm_data_change_event_log.event_time IS '事件触发时间';
COMMENT ON COLUMN acp_pm_data_change_event_log.process_start_time IS '开始处理时间';
COMMENT ON COLUMN acp_pm_data_change_event_log.process_end_time IS '处理完成时间';
COMMENT ON COLUMN acp_pm_data_change_event_log.next_retry_time IS '下次重试时间';
COMMENT ON COLUMN acp_pm_data_change_event_log.extend_info IS '扩展信息（JSON格式）';
COMMENT ON COLUMN acp_pm_data_change_event_log.is_del IS '是否删除（0-否，1-是）';
COMMENT ON COLUMN acp_pm_data_change_event_log.add_user IS '创建用户';
COMMENT ON COLUMN acp_pm_data_change_event_log.add_user_name IS '创建用户名';
COMMENT ON COLUMN acp_pm_data_change_event_log.add_time IS '创建时间';
COMMENT ON COLUMN acp_pm_data_change_event_log.update_user IS '更新用户';
COMMENT ON COLUMN acp_pm_data_change_event_log.update_user_name IS '更新用户名';
COMMENT ON COLUMN acp_pm_data_change_event_log.update_time IS '更新时间';

-- 创建索引
CREATE INDEX idx_acp_data_change_business_type ON acp_pm_data_change_event_log (business_type);
CREATE INDEX idx_acp_data_change_table_name ON acp_pm_data_change_event_log (table_name);
CREATE INDEX idx_acp_data_change_status ON acp_pm_data_change_event_log (status);
CREATE INDEX idx_acp_data_change_event_time ON acp_pm_data_change_event_log (event_time);
CREATE INDEX idx_acp_data_change_next_retry_time ON acp_pm_data_change_event_log (next_retry_time);
CREATE INDEX idx_acp_data_change_process_end_time ON acp_pm_data_change_event_log (process_end_time);
CREATE INDEX idx_acp_data_change_business_status ON acp_pm_data_change_event_log (business_type, status);
CREATE INDEX idx_acp_data_change_table_status ON acp_pm_data_change_event_log (table_name, status);
CREATE INDEX idx_acp_data_change_composite ON acp_pm_data_change_event_log (business_type, status, event_time);
CREATE INDEX idx_acp_data_change_retry ON acp_pm_data_change_event_log (status, retry_count, max_retry_count, next_retry_time);
CREATE INDEX idx_acp_data_change_cleanup ON acp_pm_data_change_event_log (status, process_end_time, is_del);

-- 业务类型配置表（可选，用于动态配置）
CREATE TABLE acp_pm_data_change_business_config (
    id VARCHAR(32) NOT NULL,
    business_type VARCHAR(50) NOT NULL,
    business_name VARCHAR(100) NOT NULL,
    description VARCHAR(500),
    enabled SMALLINT NOT NULL DEFAULT 1,
    supported_tables TEXT,
    handler_class VARCHAR(200),
    priority INTEGER NOT NULL DEFAULT 100,
    max_retry_count INTEGER,
    extend_config TEXT,
    is_del SMALLINT NOT NULL DEFAULT 0,
    add_user VARCHAR(32),
    add_user_name VARCHAR(100),
    add_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_user VARCHAR(32),
    update_user_name VARCHAR(100),
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT pk_acp_pm_data_change_business_config PRIMARY KEY (id),
    CONSTRAINT uk_acp_business_type UNIQUE (business_type)
);

-- 添加表注释
COMMENT ON TABLE acp_pm_data_change_business_config IS '数据变更业务类型配置表';

-- 添加字段注释
COMMENT ON COLUMN acp_pm_data_change_business_config.id IS '主键ID';
COMMENT ON COLUMN acp_pm_data_change_business_config.business_type IS '业务类型';
COMMENT ON COLUMN acp_pm_data_change_business_config.business_name IS '业务名称';
COMMENT ON COLUMN acp_pm_data_change_business_config.description IS '业务描述';
COMMENT ON COLUMN acp_pm_data_change_business_config.enabled IS '是否启用（0-否，1-是）';
COMMENT ON COLUMN acp_pm_data_change_business_config.supported_tables IS '支持的表名列表（JSON格式）';
COMMENT ON COLUMN acp_pm_data_change_business_config.handler_class IS '处理器类名';
COMMENT ON COLUMN acp_pm_data_change_business_config.priority IS '优先级（数字越小优先级越高）';
COMMENT ON COLUMN acp_pm_data_change_business_config.max_retry_count IS '最大重试次数';
COMMENT ON COLUMN acp_pm_data_change_business_config.extend_config IS '扩展配置（JSON格式）';
COMMENT ON COLUMN acp_pm_data_change_business_config.is_del IS '是否删除（0-否，1-是）';
COMMENT ON COLUMN acp_pm_data_change_business_config.add_user IS '创建用户';
COMMENT ON COLUMN acp_pm_data_change_business_config.add_user_name IS '创建用户名';
COMMENT ON COLUMN acp_pm_data_change_business_config.add_time IS '创建时间';
COMMENT ON COLUMN acp_pm_data_change_business_config.update_user IS '更新用户';
COMMENT ON COLUMN acp_pm_data_change_business_config.update_user_name IS '更新用户名';
COMMENT ON COLUMN acp_pm_data_change_business_config.update_time IS '更新时间';

-- 创建配置表索引
CREATE INDEX idx_acp_business_config_enabled ON acp_pm_data_change_business_config (enabled);
CREATE INDEX idx_acp_business_config_priority ON acp_pm_data_change_business_config (priority);

-- 插入默认的业务类型配置
INSERT INTO acp_pm_data_change_business_config
(id, business_type, business_name, description, enabled, supported_tables, handler_class, priority, max_retry_count)
VALUES
('prisoner_config', 'PRISONER', '监管人员', '监管人员相关数据变更处理', 1,
 '["pam_pm_prisoner", "pam_pm_prisoner_room", "pam_pm_prisoner_case"]',
 'com.rs.module.base.service.dch.handler.impl.PrisonerDataChangeHandler', 10, 3);

-- 创建更新时间自动更新的触发器
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.update_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为主表创建更新时间触发器
CREATE TRIGGER update_acp_pm_data_change_event_log_update_time
    BEFORE UPDATE ON acp_pm_data_change_event_log
    FOR EACH ROW EXECUTE FUNCTION update_modified_column();

-- 为配置表创建更新时间触发器
CREATE TRIGGER update_acp_pm_data_change_business_config_update_time
    BEFORE UPDATE ON acp_pm_data_change_business_config
    FOR EACH ROW EXECUTE FUNCTION update_modified_column();

-- PostgreSQL 通用触发器函数
-- 支持动态配置业务类型和字段映射

-- 创建通用触发器函数
CREATE OR REPLACE FUNCTION trigger_data_change_event_generic(
    business_type_param VARCHAR(50) DEFAULT 'DEFAULT',
    primary_key_field VARCHAR(50) DEFAULT 'id'
)
RETURNS TRIGGER AS $$
DECLARE
    event_id VARCHAR(32);
    old_data_json TEXT;
    new_data_json TEXT;
    pk_value TEXT;
BEGIN
    -- 生成UUID（去掉横线）
    event_id := REPLACE(gen_random_uuid()::TEXT, '-', '');

    -- 根据操作类型处理
    IF TG_OP = 'INSERT' THEN
        -- 获取主键值
        EXECUTE format('SELECT ($1).%I::TEXT', primary_key_field) INTO pk_value USING NEW;

        -- 构建新数据JSON（转换为通用格式）
        new_data_json := row_to_json(NEW)::TEXT;

        INSERT INTO acp_pm_data_change_event_log
        (id, event_type, table_name, business_type, primary_key_id, new_data, event_time)
        VALUES
        (event_id, 'INSERT', TG_TABLE_NAME, business_type_param, pk_value, new_data_json, CURRENT_TIMESTAMP);

        RETURN NEW;

    ELSIF TG_OP = 'UPDATE' THEN
        -- 获取主键值
        EXECUTE format('SELECT ($1).%I::TEXT', primary_key_field) INTO pk_value USING NEW;

        -- 构建旧数据和新数据JSON
        old_data_json := row_to_json(OLD)::TEXT;
        new_data_json := row_to_json(NEW)::TEXT;

        INSERT INTO acp_pm_data_change_event_log
        (id, event_type, table_name, business_type, primary_key_id, old_data, new_data, event_time)
        VALUES
        (event_id, 'UPDATE', TG_TABLE_NAME, business_type_param, pk_value, old_data_json, new_data_json, CURRENT_TIMESTAMP);

        RETURN NEW;

    ELSIF TG_OP = 'DELETE' THEN
        -- 获取主键值
        EXECUTE format('SELECT ($1).%I::TEXT', primary_key_field) INTO pk_value USING OLD;

        -- 构建旧数据JSON
        old_data_json := row_to_json(OLD)::TEXT;

        INSERT INTO acp_pm_data_change_event_log
        (id, event_type, table_name, business_type, primary_key_id, old_data, event_time)
        VALUES
        (event_id, 'DELETE', TG_TABLE_NAME, business_type_param, pk_value, old_data_json, CURRENT_TIMESTAMP);

        RETURN OLD;
    END IF;

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 创建监管人员专用触发器函数
CREATE OR REPLACE FUNCTION trigger_prisoner_data_change_event()
RETURNS TRIGGER AS $$
DECLARE
    event_id VARCHAR(32);
    old_data_json TEXT;
    new_data_json TEXT;
BEGIN
    -- 生成UUID（去掉横线）
    event_id := REPLACE(gen_random_uuid()::TEXT, '-', '');

    -- 根据操作类型处理
    IF TG_OP = 'INSERT' THEN
        -- 构建新数据JSON（只包含关键字段）
        new_data_json := json_build_object(
            'id', NEW.id,
            'jgrybm', NEW.jgrybm,
            'xm', NEW.xm,
            'jsh', NEW.jsh,
            'add_time', NEW.add_time
        )::TEXT;

        INSERT INTO acp_pm_data_change_event_log
        (id, event_type, table_name, business_type, primary_key_id, new_data, event_time)
        VALUES
        (event_id, 'INSERT', TG_TABLE_NAME, 'PRISONER', NEW.id::TEXT, new_data_json, CURRENT_TIMESTAMP);

        RETURN NEW;

    ELSIF TG_OP = 'UPDATE' THEN
        -- 构建旧数据JSON
        old_data_json := json_build_object(
            'id', OLD.id,
            'jgrybm', OLD.jgrybm,
            'xm', OLD.xm,
            'jsh', OLD.jsh,
            'update_time', OLD.update_time
        )::TEXT;

        -- 构建新数据JSON
        new_data_json := json_build_object(
            'id', NEW.id,
            'jgrybm', NEW.jgrybm,
            'xm', NEW.xm,
            'jsh', NEW.jsh,
            'update_time', NEW.update_time
        )::TEXT;

        INSERT INTO acp_pm_data_change_event_log
        (id, event_type, table_name, business_type, primary_key_id, old_data, new_data, event_time)
        VALUES
        (event_id, 'UPDATE', TG_TABLE_NAME, 'PRISONER', NEW.id::TEXT, old_data_json, new_data_json, CURRENT_TIMESTAMP);

        RETURN NEW;

    ELSIF TG_OP = 'DELETE' THEN
        -- 构建旧数据JSON
        old_data_json := json_build_object(
            'id', OLD.id,
            'jgrybm', OLD.jgrybm,
            'xm', OLD.xm,
            'jsh', OLD.jsh,
            'update_time', OLD.update_time
        )::TEXT;

        INSERT INTO acp_pm_data_change_event_log
        (id, event_type, table_name, business_type, primary_key_id, old_data, event_time)
        VALUES
        (event_id, 'DELETE', TG_TABLE_NAME, 'PRISONER', OLD.id::TEXT, old_data_json, CURRENT_TIMESTAMP);

        RETURN OLD;
    END IF;

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器示例

-- 方式1：使用通用触发器函数（推荐）
-- 监管人员表触发器
CREATE TRIGGER tr_pam_pm_prisoner_data_change
    AFTER INSERT OR UPDATE OR DELETE ON pam_pm_prisoner
    FOR EACH ROW EXECUTE FUNCTION trigger_data_change_event_generic('PRISONER', 'id');

-- 监管人员监室信息表触发器
CREATE TRIGGER tr_pam_pm_prisoner_room_data_change
    AFTER INSERT OR UPDATE OR DELETE ON pam_pm_prisoner_room
    FOR EACH ROW EXECUTE FUNCTION trigger_data_change_event_generic('PRISONER', 'id');

-- 监管人员案件信息表触发器
CREATE TRIGGER tr_pam_pm_prisoner_case_data_change
    AFTER INSERT OR UPDATE OR DELETE ON pam_pm_prisoner_case
    FOR EACH ROW EXECUTE FUNCTION trigger_data_change_event_generic('PRISONER', 'id');

-- 方式2：使用专用触发器函数（适用于需要特殊字段处理的场景）
-- 如果需要使用专用触发器函数，可以这样创建：
/*
CREATE TRIGGER tr_pam_pm_prisoner_data_change_custom
    AFTER INSERT OR UPDATE OR DELETE ON pam_pm_prisoner
    FOR EACH ROW EXECUTE FUNCTION trigger_prisoner_data_change_event();
*/

-- 其他业务表触发器示例
-- 案件表触发器
/*
CREATE TRIGGER tr_pam_pm_case_data_change
    AFTER INSERT OR UPDATE OR DELETE ON pam_pm_case
    FOR EACH ROW EXECUTE FUNCTION trigger_data_change_event_generic('CASE', 'id');
*/

-- 监室表触发器
/*
CREATE TRIGGER tr_pam_pm_room_data_change
    AFTER INSERT OR UPDATE OR DELETE ON pam_pm_room
    FOR EACH ROW EXECUTE FUNCTION trigger_data_change_event_generic('ROOM', 'id');
*/

-- 创建分区表（可选，用于大数据量场景）
-- 按月分区示例
/*
CREATE TABLE acp_pm_data_change_event_log_y2025m01 PARTITION OF acp_pm_data_change_event_log
FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');

CREATE TABLE acp_pm_data_change_event_log_y2025m02 PARTITION OF acp_pm_data_change_event_log
FOR VALUES FROM ('2025-02-01') TO ('2025-03-01');
*/

-- 创建清理过期数据的存储过程
CREATE OR REPLACE FUNCTION cleanup_expired_data_change_events(retention_days INTEGER DEFAULT 7)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
    expire_time TIMESTAMP;
BEGIN
    expire_time := CURRENT_TIMESTAMP - INTERVAL '1 day' * retention_days;

    UPDATE acp_pm_data_change_event_log
    SET is_del = 1, update_time = CURRENT_TIMESTAMP
    WHERE is_del = 0
      AND status = 'SUCCESS'
      AND process_end_time < expire_time;

    GET DIAGNOSTICS deleted_count = ROW_COUNT;

    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 创建统计函数
CREATE OR REPLACE FUNCTION get_data_change_event_stats()
RETURNS TABLE(
    status VARCHAR(20),
    count BIGINT,
    earliest_event TIMESTAMP,
    latest_event TIMESTAMP
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        e.status,
        COUNT(*) as count,
        MIN(e.event_time) as earliest_event,
        MAX(e.event_time) as latest_event
    FROM acp_pm_data_change_event_log e
    WHERE e.is_del = 0
    GROUP BY e.status
    ORDER BY e.status;
END;
$$ LANGUAGE plpgsql;

-- 创建查询待处理事件的函数
CREATE OR REPLACE FUNCTION get_pending_events(batch_size INTEGER DEFAULT 100)
RETURNS SETOF acp_pm_data_change_event_log AS $$
BEGIN
    RETURN QUERY
    SELECT * FROM acp_pm_data_change_event_log
    WHERE is_del = 0 AND status = 'PENDING'
    ORDER BY event_time ASC
    LIMIT batch_size;
END;
$$ LANGUAGE plpgsql;

-- 创建查询重试事件的函数
CREATE OR REPLACE FUNCTION get_retry_events(batch_size INTEGER DEFAULT 100)
RETURNS SETOF acp_pm_data_change_event_log AS $$
BEGIN
    RETURN QUERY
    SELECT * FROM acp_pm_data_change_event_log
    WHERE is_del = 0
      AND status = 'FAILED'
      AND retry_count < max_retry_count
      AND (next_retry_time IS NULL OR next_retry_time <= CURRENT_TIMESTAMP)
    ORDER BY COALESCE(next_retry_time, event_time) ASC
    LIMIT batch_size;
END;
$$ LANGUAGE plpgsql;
