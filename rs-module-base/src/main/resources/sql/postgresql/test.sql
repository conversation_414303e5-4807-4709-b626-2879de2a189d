-- PostgreSQL 数据变更事件处理系统测试脚本

\echo '开始测试数据变更事件处理系统...'

-- 1. 创建测试表
\echo '创建测试表...'

DROP TABLE IF EXISTS test_prisoner CASCADE;

CREATE TABLE test_prisoner (
    id VARCHAR(32) NOT NULL PRIMARY KEY,
    jgrybm VARCHAR(50) NOT NULL,
    xm VARCHAR(100) NOT NULL,
    jsh VARCHAR(20),
    status VARCHAR(20) DEFAULT 'ACTIVE',
    add_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 为测试表创建更新时间触发器
CREATE TRIGGER update_test_prisoner_update_time 
    BEFORE UPDATE ON test_prisoner 
    FOR EACH ROW EXECUTE FUNCTION update_modified_column();

\echo '测试表创建完成'

-- 2. 为测试表创建数据变更触发器
\echo '创建数据变更触发器...'

CREATE TRIGGER tr_test_prisoner_data_change
    AFTER INSERT OR UPDATE OR DELETE ON test_prisoner
    FOR EACH ROW EXECUTE FUNCTION trigger_data_change_event_generic('PRISONER', 'id');

\echo '触发器创建完成'

-- 3. 测试插入操作
\echo '测试插入操作...'

INSERT INTO test_prisoner (id, jgrybm, xm, jsh) VALUES 
('test001', 'P001', '张三', 'R001'),
('test002', 'P002', '李四', 'R002'),
('test003', 'P003', '王五', 'R003');

-- 检查插入事件
SELECT 
    event_type,
    table_name,
    business_type,
    primary_key_id,
    status,
    event_time
FROM acp_pm_data_change_event_log 
WHERE table_name = 'test_prisoner' AND event_type = 'INSERT'
ORDER BY event_time;

\echo '插入测试完成'

-- 4. 测试更新操作
\echo '测试更新操作...'

UPDATE test_prisoner SET xm = '张三丰', jsh = 'R004' WHERE id = 'test001';
UPDATE test_prisoner SET status = 'INACTIVE' WHERE jgrybm = 'P002';

-- 检查更新事件
SELECT 
    event_type,
    primary_key_id,
    old_data::json->>'xm' as old_name,
    new_data::json->>'xm' as new_name,
    old_data::json->>'jsh' as old_room,
    new_data::json->>'jsh' as new_room,
    event_time
FROM acp_pm_data_change_event_log 
WHERE table_name = 'test_prisoner' AND event_type = 'UPDATE'
ORDER BY event_time;

\echo '更新测试完成'

-- 5. 测试删除操作
\echo '测试删除操作...'

DELETE FROM test_prisoner WHERE id = 'test003';

-- 检查删除事件
SELECT 
    event_type,
    primary_key_id,
    old_data::json->>'xm' as deleted_name,
    old_data::json->>'jgrybm' as deleted_code,
    event_time
FROM acp_pm_data_change_event_log 
WHERE table_name = 'test_prisoner' AND event_type = 'DELETE'
ORDER BY event_time;

\echo '删除测试完成'

-- 6. 测试统计函数
\echo '测试统计函数...'

SELECT * FROM get_data_change_event_stats();

-- 7. 测试查询函数
\echo '测试查询函数...'

-- 查看所有测试事件
SELECT 
    id,
    event_type,
    primary_key_id,
    status,
    event_time,
    CASE 
        WHEN old_data IS NOT NULL THEN old_data::json->>'xm'
        ELSE NULL 
    END as old_name,
    CASE 
        WHEN new_data IS NOT NULL THEN new_data::json->>'xm'
        ELSE NULL 
    END as new_name
FROM acp_pm_data_change_event_log 
WHERE table_name = 'test_prisoner'
ORDER BY event_time;

-- 8. 测试清理函数
\echo '测试清理函数...'

-- 先将一个事件标记为成功并设置较早的完成时间
UPDATE acp_pm_data_change_event_log 
SET status = 'SUCCESS', 
    process_end_time = CURRENT_TIMESTAMP - INTERVAL '10 days'
WHERE table_name = 'test_prisoner' 
  AND primary_key_id = 'test001' 
  AND event_type = 'INSERT';

-- 执行清理（保留7天）
SELECT cleanup_expired_data_change_events(7) as cleaned_count;

-- 检查清理结果
SELECT 
    status,
    is_del,
    COUNT(*) as count
FROM acp_pm_data_change_event_log 
WHERE table_name = 'test_prisoner'
GROUP BY status, is_del;

-- 9. 性能测试（可选）
\echo '性能测试...'

-- 批量插入测试数据
DO $$
DECLARE
    i INTEGER;
BEGIN
    FOR i IN 1..100 LOOP
        INSERT INTO test_prisoner (id, jgrybm, xm, jsh) 
        VALUES 
        ('perf' || LPAD(i::TEXT, 3, '0'), 'P' || LPAD(i::TEXT, 3, '0'), '性能测试' || i, 'R' || (i % 10 + 1));
    END LOOP;
END $$;

-- 检查性能测试结果
SELECT 
    COUNT(*) as total_events,
    COUNT(*) FILTER (WHERE event_type = 'INSERT') as insert_events,
    MIN(event_time) as first_event,
    MAX(event_time) as last_event,
    MAX(event_time) - MIN(event_time) as duration
FROM acp_pm_data_change_event_log 
WHERE table_name = 'test_prisoner';

-- 10. 清理测试数据
\echo '清理测试数据...'

-- 删除测试表（会触发删除事件）
DROP TABLE test_prisoner CASCADE;

-- 清理测试事件日志
DELETE FROM acp_pm_data_change_event_log WHERE table_name = 'test_prisoner';

\echo '测试完成！'
\echo ''
\echo '测试结果总结：'
\echo '- 表和索引创建：正常'
\echo '- 触发器函数创建：正常'
\echo '- INSERT事件捕获：正常'
\echo '- UPDATE事件捕获：正常'
\echo '- DELETE事件捕获：正常'
\echo '- 统计函数：正常'
\echo '- 清理函数：正常'
\echo ''
\echo '系统已准备就绪，可以开始使用！'
