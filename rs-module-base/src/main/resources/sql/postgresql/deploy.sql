-- PostgreSQL 数据变更事件处理系统部署脚本
-- 执行方式: psql -U username -d database_name -f deploy.sql

\echo '开始部署数据变更事件处理系统...'

-- 检查PostgreSQL版本
SELECT version();

-- 1. 创建主表
\echo '创建主表 acp_pm_data_change_event_log...'

DROP TABLE IF EXISTS acp_pm_data_change_event_log CASCADE;

CREATE TABLE acp_pm_data_change_event_log (
    id VARCHAR(32) NOT NULL PRIMARY KEY,
    event_type VARCHAR(20) NOT NULL CHECK (event_type IN ('INSERT', 'UPDATE', 'DELETE')),
    table_name VARCHAR(100) NOT NULL,
    business_type VARCHAR(50) NOT NULL,
    primary_key_id VARCHAR(100) NOT NULL,
    old_data TEXT,
    new_data TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'PROCESSING', 'SUCCESS', 'FAILED', 'SKIPPED')),
    handler_class VARCHAR(200),
    retry_count INTEGER NOT NULL DEFAULT 0 CHECK (retry_count >= 0),
    max_retry_count INTEGER NOT NULL DEFAULT 3 CHECK (max_retry_count >= 0),
    error_message TEXT,
    event_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    process_start_time TIMESTAMP,
    process_end_time TIMESTAMP,
    next_retry_time TIMESTAMP,
    extend_info TEXT,
    is_del SMALLINT NOT NULL DEFAULT 0 CHECK (is_del IN (0, 1)),
    add_user VARCHAR(32),
    add_user_name VARCHAR(100),
    add_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_user VARCHAR(32),
    update_user_name VARCHAR(100),
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

\echo '主表创建完成'

-- 2. 创建索引
\echo '创建索引...'

CREATE INDEX idx_acp_data_change_status ON acp_pm_data_change_event_log (status) WHERE is_del = 0;
CREATE INDEX idx_acp_data_change_business_type ON acp_pm_data_change_event_log (business_type) WHERE is_del = 0;
CREATE INDEX idx_acp_data_change_event_time ON acp_pm_data_change_event_log (event_time) WHERE is_del = 0;
CREATE INDEX idx_acp_data_change_next_retry ON acp_pm_data_change_event_log (next_retry_time) WHERE status = 'FAILED' AND is_del = 0;
CREATE INDEX idx_acp_data_change_composite ON acp_pm_data_change_event_log (business_type, status, event_time) WHERE is_del = 0;
CREATE INDEX idx_acp_data_change_cleanup ON acp_pm_data_change_event_log (status, process_end_time) WHERE is_del = 0;

\echo '索引创建完成'

-- 3. 创建配置表
\echo '创建配置表...'

DROP TABLE IF EXISTS acp_pm_data_change_business_config CASCADE;

CREATE TABLE acp_pm_data_change_business_config (
    id VARCHAR(32) NOT NULL PRIMARY KEY,
    business_type VARCHAR(50) NOT NULL UNIQUE,
    business_name VARCHAR(100) NOT NULL,
    description VARCHAR(500),
    enabled SMALLINT NOT NULL DEFAULT 1 CHECK (enabled IN (0, 1)),
    supported_tables TEXT,
    handler_class VARCHAR(200),
    priority INTEGER NOT NULL DEFAULT 100,
    max_retry_count INTEGER CHECK (max_retry_count >= 0),
    extend_config TEXT,
    is_del SMALLINT NOT NULL DEFAULT 0 CHECK (is_del IN (0, 1)),
    add_user VARCHAR(32),
    add_user_name VARCHAR(100),
    add_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_user VARCHAR(32),
    update_user_name VARCHAR(100),
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

\echo '配置表创建完成'

-- 4. 创建触发器函数
\echo '创建触发器函数...'

-- 更新时间触发器函数
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.update_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为主表和配置表添加更新时间触发器
CREATE TRIGGER update_acp_pm_data_change_event_log_update_time
    BEFORE UPDATE ON acp_pm_data_change_event_log
    FOR EACH ROW EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER update_acp_pm_data_change_business_config_update_time
    BEFORE UPDATE ON acp_pm_data_change_business_config
    FOR EACH ROW EXECUTE FUNCTION update_modified_column();

-- 通用数据变更触发器函数
CREATE OR REPLACE FUNCTION trigger_data_change_event_generic(
    business_type_param VARCHAR(50) DEFAULT 'DEFAULT',
    primary_key_field VARCHAR(50) DEFAULT 'id'
)
RETURNS TRIGGER AS $$
DECLARE
    event_id VARCHAR(32);
    old_data_json TEXT;
    new_data_json TEXT;
    pk_value TEXT;
BEGIN
    -- 生成UUID（去掉横线）
    event_id := REPLACE(gen_random_uuid()::TEXT, '-', '');

    -- 根据操作类型处理
    IF TG_OP = 'INSERT' THEN
        EXECUTE format('SELECT ($1).%I::TEXT', primary_key_field) INTO pk_value USING NEW;
        new_data_json := row_to_json(NEW)::TEXT;

        INSERT INTO acp_pm_data_change_event_log
        (id, event_type, table_name, business_type, primary_key_id, new_data, event_time)
        VALUES
        (event_id, 'INSERT', TG_TABLE_NAME, business_type_param, pk_value, new_data_json, CURRENT_TIMESTAMP);

        RETURN NEW;

    ELSIF TG_OP = 'UPDATE' THEN
        EXECUTE format('SELECT ($1).%I::TEXT', primary_key_field) INTO pk_value USING NEW;
        old_data_json := row_to_json(OLD)::TEXT;
        new_data_json := row_to_json(NEW)::TEXT;

        INSERT INTO acp_pm_data_change_event_log
        (id, event_type, table_name, business_type, primary_key_id, old_data, new_data, event_time)
        VALUES
        (event_id, 'UPDATE', TG_TABLE_NAME, business_type_param, pk_value, old_data_json, new_data_json, CURRENT_TIMESTAMP);

        RETURN NEW;

    ELSIF TG_OP = 'DELETE' THEN
        EXECUTE format('SELECT ($1).%I::TEXT', primary_key_field) INTO pk_value USING OLD;
        old_data_json := row_to_json(OLD)::TEXT;

        INSERT INTO acp_pm_data_change_event_log
        (id, event_type, table_name, business_type, primary_key_id, old_data, event_time)
        VALUES
        (event_id, 'DELETE', TG_TABLE_NAME, business_type_param, pk_value, old_data_json, CURRENT_TIMESTAMP);

        RETURN OLD;
    END IF;

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

\echo '触发器函数创建完成'

-- 5. 创建工具函数
\echo '创建工具函数...'

-- 清理过期数据函数
CREATE OR REPLACE FUNCTION cleanup_expired_data_change_events(retention_days INTEGER DEFAULT 7)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
    expire_time TIMESTAMP;
BEGIN
    expire_time := CURRENT_TIMESTAMP - INTERVAL '1 day' * retention_days;

    UPDATE acp_pm_data_change_event_log
    SET is_del = 1, update_time = CURRENT_TIMESTAMP
    WHERE is_del = 0
      AND status = 'SUCCESS'
      AND process_end_time < expire_time;

    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 统计函数
CREATE OR REPLACE FUNCTION get_data_change_event_stats()
RETURNS TABLE(
    status VARCHAR(20),
    count BIGINT,
    earliest_event TIMESTAMP,
    latest_event TIMESTAMP
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        e.status,
        COUNT(*) as count,
        MIN(e.event_time) as earliest_event,
        MAX(e.event_time) as latest_event
    FROM acp_pm_data_change_event_log e
    WHERE e.is_del = 0
    GROUP BY e.status
    ORDER BY e.status;
END;
$$ LANGUAGE plpgsql;

\echo '工具函数创建完成'

-- 6. 插入默认配置
\echo '插入默认配置...'

INSERT INTO acp_pm_data_change_business_config
(id, business_type, business_name, description, enabled, supported_tables, handler_class, priority, max_retry_count)
VALUES
('prisoner_config', 'PRISONER', '监管人员', '监管人员相关数据变更处理', 1,
 '["pam_pm_prisoner", "pam_pm_prisoner_room", "pam_pm_prisoner_case"]',
 'com.rs.module.base.service.dch.handler.impl.PrisonerDataChangeHandler', 10, 3);

\echo '默认配置插入完成'

-- 7. 验证部署
\echo '验证部署结果...'

-- 检查表
SELECT 'acp_pm_data_change_event_log' as table_name,
       CASE WHEN EXISTS (SELECT 1 FROM pg_tables WHERE tablename = 'acp_pm_data_change_event_log')
            THEN '✓ 存在' ELSE '✗ 不存在' END as status
UNION ALL
SELECT 'acp_pm_data_change_business_config' as table_name,
       CASE WHEN EXISTS (SELECT 1 FROM pg_tables WHERE tablename = 'acp_pm_data_change_business_config')
            THEN '✓ 存在' ELSE '✗ 不存在' END as status;

-- 检查函数
SELECT routine_name,
       CASE WHEN routine_name IS NOT NULL THEN '✓ 存在' ELSE '✗ 不存在' END as status
FROM information_schema.routines
WHERE routine_name IN ('trigger_data_change_event_generic', 'cleanup_expired_data_change_events', 'get_data_change_event_stats');

-- 检查配置数据
SELECT business_type, business_name, enabled FROM acp_pm_data_change_business_config WHERE is_del = 0;

\echo '部署完成！'
\echo ''
\echo '下一步操作：'
\echo '1. 为需要监听的业务表创建触发器'
\echo '2. 配置应用程序连接参数'
\echo '3. 启动应用程序'
\echo '4. 配置XXL-JOB定时任务'
\echo ''
\echo '示例：为表创建触发器'
\echo 'CREATE TRIGGER tr_your_table_data_change'
\echo '    AFTER INSERT OR UPDATE OR DELETE ON your_table'
\echo '    FOR EACH ROW EXECUTE FUNCTION trigger_data_change_event_generic(''YOUR_BUSINESS_TYPE'', ''id'');'
