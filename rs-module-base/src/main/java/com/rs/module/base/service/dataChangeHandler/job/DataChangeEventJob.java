package com.rs.module.base.service.dataChangeHandler.job;

import com.bsp.common.util.StringUtil;
import com.rs.module.base.service.dataChangeHandler.DataChangeEventLogService;
import com.rs.module.base.service.dataChangeHandler.config.DataChangeEventConfig;
import com.rs.module.base.service.dataChangeHandler.processor.DataChangeEventProcessor;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Data Change Event Job (XXL-JOB)
 *
 * <AUTHOR>
 * @date 2025/01/05
 */
@Slf4j
@Component
@ConditionalOnProperty(prefix = "rs.data-change-event", name = "enabled", havingValue = "true", matchIfMissing = true)
public class DataChangeEventJob {

    @Autowired
    private DataChangeEventProcessor eventProcessor;

    @Autowired
    private DataChangeEventLogService eventLogService;

    @Autowired
    private DataChangeEventConfig config;

    /**
     * Processing pending events flag
     */
    public static final AtomicBoolean PROCESSING_PENDING_EVENTS = new AtomicBoolean(false);

    /**
     * Processing retry events flag
     */
    public static final AtomicBoolean PROCESSING_RETRY_EVENTS = new AtomicBoolean(false);

    /**
     * Cleaning expired events flag
     */
    public static final AtomicBoolean CLEANING_EXPIRED_EVENTS = new AtomicBoolean(false);

    /**
     * 处理待处理的事件
     */
    @XxlJob("processPendingEvents")
    @Transactional
    public void processPendingEvents() {
        if (!config.isEnabled()) {
            return;
        }

        if (PROCESSING_PENDING_EVENTS.compareAndSet(false, true)) {
            // 当前没有线程正在执行该方法，将标志设置为 true，表示当前线程正在执行该方法
            try {
                String uuid = StringUtil.getGuid32();
                String jobParam = XxlJobHelper.getJobParam();
                int batchSize = config.getBatchSize();

                // 支持通过任务参数传递批处理大小
                if (jobParam != null && !jobParam.trim().isEmpty()) {
                    try {
                        batchSize = Integer.parseInt(jobParam.trim());
                    } catch (NumberFormatException e) {
                        log.warn("[{}]任务参数格式错误，使用默认批处理大小: {}", uuid, config.getBatchSize());
                        XxlJobHelper.log("[{}]任务参数格式错误，使用默认批处理大小: {}", uuid, config.getBatchSize());
                    }
                }

                log.debug("[{}]开始处理待处理的数据变更事件，批处理大小: {}", uuid, batchSize);
                XxlJobHelper.log("[{}]开始处理待处理的数据变更事件，批处理大小: {}", uuid, batchSize);
                long startTime = System.currentTimeMillis();

                int processedCount = eventProcessor.processPendingEvents(batchSize);

                double duration = (System.currentTimeMillis() - startTime) / 1000.0;
                if (processedCount > 0) {
                    log.info("[{}]处理待处理事件完成: 处理数量={}, 用时: {}s", uuid, processedCount, duration);
                    XxlJobHelper.log("[{}]处理待处理事件完成: 处理数量={}, 用时: {}s", uuid, processedCount, duration);
                } else {
                    log.debug("[{}]处理待处理事件完成: 无待处理事件, 用时: {}s", uuid, duration);
                    XxlJobHelper.log("[{}]处理待处理事件完成: 无待处理事件, 用时: {}s", uuid, duration);
                }
            } catch (Exception e) {
                log.error("处理待处理事件异常", e);
                XxlJobHelper.handleFail("处理待处理事件异常: " + e.getMessage());
                e.printStackTrace();
            } finally {
                // 方法执行完毕，将标志设置为 false，表示该方法可以被其他线程执行
                PROCESSING_PENDING_EVENTS.set(false);
            }
        }
    }

    /**
     * 处理需要重试的事件
     */
    @XxlJob("processRetryEvents")
    @Transactional
    public void processRetryEvents() {
        if (!config.isEnabled()) {
            return;
        }

        if (PROCESSING_RETRY_EVENTS.compareAndSet(false, true)) {
            // 当前没有线程正在执行该方法，将标志设置为 true，表示当前线程正在执行该方法
            try {
                String uuid = StringUtil.getGuid32();
                String jobParam = XxlJobHelper.getJobParam();
                int batchSize = config.getBatchSize();

                // 支持通过任务参数传递批处理大小
                if (jobParam != null && !jobParam.trim().isEmpty()) {
                    try {
                        batchSize = Integer.parseInt(jobParam.trim());
                    } catch (NumberFormatException e) {
                        log.warn("[{}]任务参数格式错误，使用默认批处理大小: {}", uuid, config.getBatchSize());
                        XxlJobHelper.log("[{}]任务参数格式错误，使用默认批处理大小: {}", uuid, config.getBatchSize());
                    }
                }

                log.debug("[{}]开始处理需要重试的数据变更事件，批处理大小: {}", uuid, batchSize);
                XxlJobHelper.log("[{}]开始处理需要重试的数据变更事件，批处理大小: {}", uuid, batchSize);
                long startTime = System.currentTimeMillis();

                int processedCount = eventProcessor.processRetryEvents(batchSize);

                double duration = (System.currentTimeMillis() - startTime) / 1000.0;
                if (processedCount > 0) {
                    log.info("[{}]处理重试事件完成: 处理数量={}, 用时: {}s", uuid, processedCount, duration);
                    XxlJobHelper.log("[{}]处理重试事件完成: 处理数量={}, 用时: {}s", uuid, processedCount, duration);
                } else {
                    log.debug("[{}]处理重试事件完成: 无重试事件, 用时: {}s", uuid, duration);
                    XxlJobHelper.log("[{}]处理重试事件完成: 无重试事件, 用时: {}s", uuid, duration);
                }
            } catch (Exception e) {
                log.error("处理重试事件异常", e);
                XxlJobHelper.handleFail("处理重试事件异常: " + e.getMessage());
                e.printStackTrace();
            } finally {
                // 方法执行完毕，将标志设置为 false，表示该方法可以被其他线程执行
                PROCESSING_RETRY_EVENTS.set(false);
            }
        }
    }

    /**
     * 清理过期的事件
     */
    @XxlJob("cleanupExpiredEvents")
    @Transactional
    public void cleanupExpiredEvents() {
        if (!config.isEnabled()) {
            return;
        }

        if (CLEANING_EXPIRED_EVENTS.compareAndSet(false, true)) {
            // 当前没有线程正在执行该方法，将标志设置为 true，表示当前线程正在执行该方法
            try {
                String uuid = StringUtil.getGuid32();
                String jobParam = XxlJobHelper.getJobParam();
                int retentionDays = config.getSuccessEventRetentionDays();

                // 支持通过任务参数传递保留天数
                if (jobParam != null && !jobParam.trim().isEmpty()) {
                    try {
                        retentionDays = Integer.parseInt(jobParam.trim());
                    } catch (NumberFormatException e) {
                        log.warn("[{}]任务参数格式错误，使用默认保留天数: {}", uuid, config.getSuccessEventRetentionDays());
                        XxlJobHelper.log("[{}]任务参数格式错误，使用默认保留天数: {}", uuid, config.getSuccessEventRetentionDays());
                    }
                }

                log.debug("[{}]开始清理过期的数据变更事件，保留天数: {}", uuid, retentionDays);
                XxlJobHelper.log("[{}]开始清理过期的数据变更事件，保留天数: {}", uuid, retentionDays);
                long startTime = System.currentTimeMillis();

                // 清理过期的成功事件
                int deletedCount = eventLogService.deleteExpiredSuccessEvents(retentionDays);

                double duration = (System.currentTimeMillis() - startTime) / 1000.0;
                if (deletedCount > 0) {
                    log.info("[{}]清理过期事件完成: 清理数量={}, 用时: {}s", uuid, deletedCount, duration);
                    XxlJobHelper.log("[{}]清理过期事件完成: 清理数量={}, 用时: {}s", uuid, deletedCount, duration);
                } else {
                    log.debug("[{}]清理过期事件完成: 无过期事件, 用时: {}s", uuid, duration);
                    XxlJobHelper.log("[{}]清理过期事件完成: 无过期事件, 用时: {}s", uuid, duration);
                }
            } catch (Exception e) {
                log.error("清理过期事件异常", e);
                XxlJobHelper.handleFail("清理过期事件异常: " + e.getMessage());
                e.printStackTrace();
            } finally {
                // 方法执行完毕，将标志设置为 false，表示该方法可以被其他线程执行
                CLEANING_EXPIRED_EVENTS.set(false);
            }
        }
    }

    /**
     * 处理指定业务类型的事件
     * 任务参数格式: businessType,limit 例如: PRISONER,100
     */
    @XxlJob("processEventsByBusinessType")
    @Transactional
    public void processEventsByBusinessType() {
        String uuid = StringUtil.getGuid32();
        String jobParam = XxlJobHelper.getJobParam();

        if (jobParam == null || jobParam.trim().isEmpty()) {
            String errorMsg = String.format("[%s]任务参数不能为空，格式: businessType,limit 例如: PRISONER,100", uuid);
            log.error(errorMsg);
            XxlJobHelper.log(errorMsg);
            XxlJobHelper.handleFail(errorMsg);
            return;
        }

        String[] params = jobParam.trim().split(",");
        if (params.length < 1 || params.length > 2) {
            String errorMsg = String.format("[%s]任务参数格式错误，格式: businessType,limit 例如: PRISONER,100", uuid);
            log.error(errorMsg);
            XxlJobHelper.log(errorMsg);
            XxlJobHelper.handleFail(errorMsg);
            return;
        }

        String businessType = params[0].trim();
        int limit = config.getBatchSize();

        if (params.length == 2) {
            try {
                limit = Integer.parseInt(params[1].trim());
            } catch (NumberFormatException e) {
                log.warn("[{}]限制数量参数格式错误，使用默认值: {}", uuid, config.getBatchSize());
                XxlJobHelper.log("[{}]限制数量参数格式错误，使用默认值: {}", uuid, config.getBatchSize());
            }
        }

        processEventsByBusinessTypeWithUuid(businessType, limit, uuid);
    }

    /**
     * 处理指定业务类型的事件（内部方法，支持手动调用）
     *
     * @param businessType 业务类型
     * @param limit        限制数量
     * @return 处理数量
     */
    public int processEventsByBusinessTypeInternal(String businessType, int limit) {
        String uuid = StringUtil.getGuid32();

        if (!config.isEnabled()) {
            String errorMsg = String.format("[%s]数据变更事件处理已禁用", uuid);
            log.warn(errorMsg);
            return 0;
        }

        if (!config.isBusinessTypeEnabled(businessType)) {
            String errorMsg = String.format("[%s]业务类型 %s 已禁用", uuid, businessType);
            log.warn(errorMsg);
            return 0;
        }

        try {
            log.info("[{}]开始处理业务类型 {} 的事件，限制数量: {}", uuid, businessType, limit);
            long startTime = System.currentTimeMillis();

            int processedCount = eventProcessor.processEventsByBusinessType(businessType, limit);

            double duration = (System.currentTimeMillis() - startTime) / 1000.0;
            log.info("[{}]处理业务类型 {} 的事件完成: 处理数量={}, 用时: {}s", uuid, businessType, processedCount, duration);

            return processedCount;
        } catch (Exception e) {
            log.error("[{}]处理业务类型 {} 的事件异常", uuid, businessType, e);
            e.printStackTrace();
            return 0;
        }
    }

    /**
     * 处理指定业务类型的事件（带UUID的内部方法）
     *
     * @param businessType 业务类型
     * @param limit        限制数量
     * @param uuid         任务UUID
     * @return 处理数量
     */
    private int processEventsByBusinessTypeWithUuid(String businessType, int limit, String uuid) {
        if (!config.isEnabled()) {
            String errorMsg = String.format("[%s]数据变更事件处理已禁用", uuid);
            log.warn(errorMsg);
            XxlJobHelper.log(errorMsg);
            return 0;
        }

        if (!config.isBusinessTypeEnabled(businessType)) {
            String errorMsg = String.format("[%s]业务类型 %s 已禁用", uuid, businessType);
            log.warn(errorMsg);
            XxlJobHelper.log(errorMsg);
            return 0;
        }

        try {
            log.info("[{}]开始处理业务类型 {} 的事件，限制数量: {}", uuid, businessType, limit);
            XxlJobHelper.log("[{}]开始处理业务类型 {} 的事件，限制数量: {}", uuid, businessType, limit);
            long startTime = System.currentTimeMillis();

            int processedCount = eventProcessor.processEventsByBusinessType(businessType, limit);

            double duration = (System.currentTimeMillis() - startTime) / 1000.0;
            log.info("[{}]处理业务类型 {} 的事件完成: 处理数量={}, 用时: {}s", uuid, businessType, processedCount, duration);
            XxlJobHelper.log("[{}]处理业务类型 {} 的事件完成: 处理数量={}, 用时: {}s", uuid, businessType, processedCount, duration);

            return processedCount;
        } catch (Exception e) {
            log.error("[{}]处理业务类型 {} 的事件异常", uuid, businessType, e);
            XxlJobHelper.log("[{}]处理业务类型 {} 的事件异常: {}", uuid, businessType, e.getMessage());
            XxlJobHelper.handleFail("处理业务类型事件异常: " + e.getMessage());
            e.printStackTrace();
            return 0;
        }
    }

    /**
     * 获取任务运行状态
     *
     * @return 状态信息
     */
    public String getJobStatus() {
        StringBuilder status = new StringBuilder();
        status.append("数据变更事件处理任务状态:\n");
        status.append("- 系统启用状态: ").append(config.isEnabled()).append("\n");
        status.append("- 待处理事件任务运行中: ").append(PROCESSING_PENDING_EVENTS.get()).append("\n");
        status.append("- 重试事件任务运行中: ").append(PROCESSING_RETRY_EVENTS.get()).append("\n");
        status.append("- 清理过期事件任务运行中: ").append(CLEANING_EXPIRED_EVENTS.get()).append("\n");
        status.append("- 批处理大小: ").append(config.getBatchSize()).append("\n");
        status.append("- 最大重试次数: ").append(config.getMaxRetryCount()).append("\n");
        return status.toString();
    }
}
