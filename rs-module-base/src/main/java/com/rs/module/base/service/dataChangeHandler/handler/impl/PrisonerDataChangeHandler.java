package com.rs.module.base.service.dataChangeHandler.handler.impl;

import com.rs.module.base.service.dataChangeHandler.context.DataChangeEventContext;
import com.rs.module.base.service.dataChangeHandler.enums.DataChangeEventTypeEnum;
import com.rs.module.base.service.dataChangeHandler.handler.AbstractDataChangeEventHandler;
import com.rs.module.base.service.dataChangeHandler.handler.DataChangeEventHandlerResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 监管人员数据变更处理器示例
 *
 * <AUTHOR>
 * @date 2025/01/05
 */
@Slf4j
@Component
public class PrisonerDataChangeHandler extends AbstractDataChangeEventHandler {

    private static final String BUSINESS_TYPE = "PRISONER";

    @Override
    public String getSupportedBusinessType() {
        return BUSINESS_TYPE;
    }

    @Override
    public int getPriority() {
        return 10; // 高优先级
    }

    @Override
    protected List<String> getSupportedTableNames() {
        return Arrays.asList(
            "pam_pm_prisoner",           // 监管人员基础信息表
            "pam_pm_prisoner_room",      // 监管人员监室信息表
            "pam_pm_prisoner_case"       // 监管人员案件信息表
        );
    }

    @Override
    protected Set<DataChangeEventTypeEnum> getSupportedEventTypes() {
        return new HashSet<>(Arrays.asList(
            DataChangeEventTypeEnum.INSERT,
            DataChangeEventTypeEnum.UPDATE,
            DataChangeEventTypeEnum.DELETE
        ));
    }

    @Override
    protected DataChangeEventHandlerResult doHandle(DataChangeEventContext context) {
        try {
            switch (context.getEventType()) {
                case INSERT:
                    return handleInsert(context);
                case UPDATE:
                    return handleUpdate(context);
                case DELETE:
                    return handleDelete(context);
                default:
                    return DataChangeEventHandlerResult.failure("不支持的事件类型: " + context.getEventType());
            }
        } catch (Exception e) {
            log.error("处理监管人员数据变更异常", e);
            return DataChangeEventHandlerResult.failure("处理异常: " + e.getMessage());
        }
    }

    /**
     * 处理插入事件
     */
    private DataChangeEventHandlerResult handleInsert(DataChangeEventContext context) {
        log.info("处理监管人员新增事件: 表名={}, 主键ID={}", context.getTableName(), context.getPrimaryKeyId());
        
        switch (context.getTableName()) {
            case "pam_pm_prisoner":
                return handlePrisonerInsert(context);
            case "pam_pm_prisoner_room":
                return handlePrisonerRoomInsert(context);
            case "pam_pm_prisoner_case":
                return handlePrisonerCaseInsert(context);
            default:
                return DataChangeEventHandlerResult.success("无需处理的表: " + context.getTableName());
        }
    }

    /**
     * 处理更新事件
     */
    private DataChangeEventHandlerResult handleUpdate(DataChangeEventContext context) {
        log.info("处理监管人员更新事件: 表名={}, 主键ID={}", context.getTableName(), context.getPrimaryKeyId());
        
        switch (context.getTableName()) {
            case "pam_pm_prisoner":
                return handlePrisonerUpdate(context);
            case "pam_pm_prisoner_room":
                return handlePrisonerRoomUpdate(context);
            case "pam_pm_prisoner_case":
                return handlePrisonerCaseUpdate(context);
            default:
                return DataChangeEventHandlerResult.success("无需处理的表: " + context.getTableName());
        }
    }

    /**
     * 处理删除事件
     */
    private DataChangeEventHandlerResult handleDelete(DataChangeEventContext context) {
        log.info("处理监管人员删除事件: 表名={}, 主键ID={}", context.getTableName(), context.getPrimaryKeyId());
        
        switch (context.getTableName()) {
            case "pam_pm_prisoner":
                return handlePrisonerDelete(context);
            case "pam_pm_prisoner_room":
                return handlePrisonerRoomDelete(context);
            case "pam_pm_prisoner_case":
                return handlePrisonerCaseDelete(context);
            default:
                return DataChangeEventHandlerResult.success("无需处理的表: " + context.getTableName());
        }
    }

    /**
     * 处理监管人员基础信息新增
     */
    private DataChangeEventHandlerResult handlePrisonerInsert(DataChangeEventContext context) {
        // 示例：监管人员新增时的业务逻辑
        // 1. 发送通知
        // 2. 更新统计信息
        // 3. 同步到其他系统
        
        String prisonerName = (String) getNewValue(context, "xm");
        String prisonerCode = (String) getNewValue(context, "jgrybm");
        
        log.info("监管人员新增: 姓名={}, 编码={}", prisonerName, prisonerCode);
        
        // 这里可以添加具体的业务逻辑
        // 例如：发送消息、更新缓存、同步数据等
        
        return DataChangeEventHandlerResult.success("监管人员新增处理完成");
    }

    /**
     * 处理监管人员基础信息更新
     */
    private DataChangeEventHandlerResult handlePrisonerUpdate(DataChangeEventContext context) {
        // 示例：检查关键字段是否发生变更
        if (isFieldChanged(context, "xm")) {
            String oldName = (String) getOldValue(context, "xm");
            String newName = (String) getNewValue(context, "xm");
            log.info("监管人员姓名变更: {} -> {}", oldName, newName);
            
            // 处理姓名变更的业务逻辑
        }
        
        if (isFieldChanged(context, "jsh")) {
            String oldRoom = (String) getOldValue(context, "jsh");
            String newRoom = (String) getNewValue(context, "jsh");
            log.info("监管人员监室变更: {} -> {}", oldRoom, newRoom);
            
            // 处理监室变更的业务逻辑
        }
        
        return DataChangeEventHandlerResult.success("监管人员更新处理完成");
    }

    /**
     * 处理监管人员基础信息删除
     */
    private DataChangeEventHandlerResult handlePrisonerDelete(DataChangeEventContext context) {
        String prisonerName = (String) getOldValue(context, "xm");
        String prisonerCode = (String) getOldValue(context, "jgrybm");
        
        log.info("监管人员删除: 姓名={}, 编码={}", prisonerName, prisonerCode);
        
        // 处理删除的业务逻辑
        // 例如：清理相关数据、发送通知等
        
        return DataChangeEventHandlerResult.success("监管人员删除处理完成");
    }

    /**
     * 处理监管人员监室信息新增
     */
    private DataChangeEventHandlerResult handlePrisonerRoomInsert(DataChangeEventContext context) {
        log.info("监管人员监室信息新增处理");
        // 具体业务逻辑
        return DataChangeEventHandlerResult.success("监管人员监室信息新增处理完成");
    }

    /**
     * 处理监管人员监室信息更新
     */
    private DataChangeEventHandlerResult handlePrisonerRoomUpdate(DataChangeEventContext context) {
        log.info("监管人员监室信息更新处理");
        // 具体业务逻辑
        return DataChangeEventHandlerResult.success("监管人员监室信息更新处理完成");
    }

    /**
     * 处理监管人员监室信息删除
     */
    private DataChangeEventHandlerResult handlePrisonerRoomDelete(DataChangeEventContext context) {
        log.info("监管人员监室信息删除处理");
        // 具体业务逻辑
        return DataChangeEventHandlerResult.success("监管人员监室信息删除处理完成");
    }

    /**
     * 处理监管人员案件信息新增
     */
    private DataChangeEventHandlerResult handlePrisonerCaseInsert(DataChangeEventContext context) {
        log.info("监管人员案件信息新增处理");
        // 具体业务逻辑
        return DataChangeEventHandlerResult.success("监管人员案件信息新增处理完成");
    }

    /**
     * 处理监管人员案件信息更新
     */
    private DataChangeEventHandlerResult handlePrisonerCaseUpdate(DataChangeEventContext context) {
        log.info("监管人员案件信息更新处理");
        // 具体业务逻辑
        return DataChangeEventHandlerResult.success("监管人员案件信息更新处理完成");
    }

    /**
     * 处理监管人员案件信息删除
     */
    private DataChangeEventHandlerResult handlePrisonerCaseDelete(DataChangeEventContext context) {
        log.info("监管人员案件信息删除处理");
        // 具体业务逻辑
        return DataChangeEventHandlerResult.success("监管人员案件信息删除处理完成");
    }

    @Override
    public void onSuccess(DataChangeEventContext context, DataChangeEventHandlerResult result) {
        log.debug("监管人员数据变更处理成功: 表名={}, 主键ID={}, 耗时={}ms", 
                 context.getTableName(), context.getPrimaryKeyId(), result.getProcessingTime());
    }

    @Override
    public void onFailure(DataChangeEventContext context, Exception exception) {
        log.error("监管人员数据变更处理失败: 表名={}, 主键ID={}", 
                 context.getTableName(), context.getPrimaryKeyId(), exception);
    }
}
