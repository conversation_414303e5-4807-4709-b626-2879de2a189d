package com.rs.module.base.service.dataChangeHandler.enums;

import com.baomidou.mybatisplus.core.enums.IEnum;

/**
 * 数据变更事件状态枚举
 *
 * <AUTHOR>
 * @date 2025/01/05
 */
public enum DataChangeEventStatusEnum implements IEnum<String> {

    /**
     * 待处理
     */
    PENDING("PENDING", "待处理"),

    /**
     * 处理中
     */
    PROCESSING("PROCESSING", "处理中"),

    /**
     * 处理成功
     */
    SUCCESS("SUCCESS", "处理成功"),

    /**
     * 处理失败
     */
    FAILED("FAILED", "处理失败"),

    /**
     * 已跳过
     */
    SKIPPED("SKIPPED", "已跳过");

    private final String value;
    private final String description;

    DataChangeEventStatusEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public String getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据值获取枚举
     *
     * @param value 值
     * @return 枚举
     */
    public static DataChangeEventStatusEnum getByValue(String value) {
        for (DataChangeEventStatusEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }
}
