package com.rs.module.base.service.dataChangeHandler.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据变更事件配置
 *
 * <AUTHOR>
 * @date 2025/01/05
 */
@Data
@Component
@ConfigurationProperties(prefix = "rs.data-change-event")
public class DataChangeEventConfig {

    /**
     * 是否启用数据变更事件处理
     */
    private boolean enabled = true;

    /**
     * 是否启用异步处理
     */
    private boolean asyncEnabled = true;

    /**
     * 批处理大小
     */
    private int batchSize = 100;

    /**
     * 最大重试次数
     */
    private int maxRetryCount = 3;

    /**
     * 成功事件保留天数
     */
    private int successEventRetentionDays = 7;

    /**
     * 失败事件保留天数
     */
    private int failedEventRetentionDays = 30;

    /**
     * 定时任务配置
     */
    private ScheduleConfig schedule = new ScheduleConfig();

    /**
     * 业务类型映射配置
     */
    private Map<String, BusinessTypeConfig> businessTypes = new HashMap<>();

    @Data
    public static class ScheduleConfig {
        /**
         * 是否启用定时任务
         */
        private boolean enabled = true;

        /**
         * 处理待处理事件的cron表达式
         */
        private String pendingEventsCron = "0 */1 * * * ?"; // 每分钟执行一次

        /**
         * 处理重试事件的cron表达式
         */
        private String retryEventsCron = "0 */5 * * * ?"; // 每5分钟执行一次

        /**
         * 清理过期事件的cron表达式
         */
        private String cleanupEventsCron = "0 0 2 * * ?"; // 每天凌晨2点执行
    }

    @Data
    public static class BusinessTypeConfig {
        /**
         * 业务类型名称
         */
        private String name;

        /**
         * 业务类型描述
         */
        private String description;

        /**
         * 是否启用
         */
        private boolean enabled = true;

        /**
         * 支持的表名列表
         */
        private List<String> supportedTables;

        /**
         * 处理器类名
         */
        private String handlerClass;

        /**
         * 优先级
         */
        private int priority = 100;

        /**
         * 最大重试次数（覆盖全局配置）
         */
        private Integer maxRetryCount;

        /**
         * 扩展配置
         */
        private Map<String, Object> extendConfig = new HashMap<>();
    }

    /**
     * 获取业务类型配置
     *
     * @param businessType 业务类型
     * @return 配置信息
     */
    public BusinessTypeConfig getBusinessTypeConfig(String businessType) {
        return businessTypes.get(businessType);
    }

    /**
     * 添加业务类型配置
     *
     * @param businessType 业务类型
     * @param config       配置信息
     */
    public void addBusinessTypeConfig(String businessType, BusinessTypeConfig config) {
        businessTypes.put(businessType, config);
    }

    /**
     * 获取业务类型的最大重试次数
     *
     * @param businessType 业务类型
     * @return 最大重试次数
     */
    public int getMaxRetryCount(String businessType) {
        BusinessTypeConfig config = getBusinessTypeConfig(businessType);
        if (config != null && config.getMaxRetryCount() != null) {
            return config.getMaxRetryCount();
        }
        return maxRetryCount;
    }

    /**
     * 判断业务类型是否启用
     *
     * @param businessType 业务类型
     * @return 是否启用
     */
    public boolean isBusinessTypeEnabled(String businessType) {
        BusinessTypeConfig config = getBusinessTypeConfig(businessType);
        return config == null || config.isEnabled();
    }
}
