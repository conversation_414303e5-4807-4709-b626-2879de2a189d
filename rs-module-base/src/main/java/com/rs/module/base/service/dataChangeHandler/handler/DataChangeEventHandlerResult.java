package com.rs.module.base.service.dataChangeHandler.handler;

import lombok.Data;

import java.util.Map;

/**
 * 数据变更事件处理结果
 *
 * <AUTHOR>
 * @date 2025/01/05
 */
@Data
public class DataChangeEventHandlerResult {

    /**
     * 处理是否成功
     */
    private boolean success;

    /**
     * 处理消息
     */
    private String message;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 是否需要重试
     */
    private boolean needRetry;

    /**
     * 处理耗时（毫秒）
     */
    private long processingTime;

    /**
     * 扩展信息
     */
    private Map<String, Object> extendInfo;

    /**
     * 创建成功结果
     *
     * @return 成功结果
     */
    public static DataChangeEventHandlerResult success() {
        return success("处理成功");
    }

    /**
     * 创建成功结果
     *
     * @param message 消息
     * @return 成功结果
     */
    public static DataChangeEventHandlerResult success(String message) {
        DataChangeEventHandlerResult result = new DataChangeEventHandlerResult();
        result.setSuccess(true);
        result.setMessage(message);
        result.setNeedRetry(false);
        return result;
    }

    /**
     * 创建失败结果
     *
     * @param message 错误消息
     * @return 失败结果
     */
    public static DataChangeEventHandlerResult failure(String message) {
        return failure(message, true);
    }

    /**
     * 创建失败结果
     *
     * @param message   错误消息
     * @param needRetry 是否需要重试
     * @return 失败结果
     */
    public static DataChangeEventHandlerResult failure(String message, boolean needRetry) {
        DataChangeEventHandlerResult result = new DataChangeEventHandlerResult();
        result.setSuccess(false);
        result.setMessage(message);
        result.setNeedRetry(needRetry);
        return result;
    }

    /**
     * 创建失败结果
     *
     * @param message   错误消息
     * @param errorCode 错误码
     * @param needRetry 是否需要重试
     * @return 失败结果
     */
    public static DataChangeEventHandlerResult failure(String message, String errorCode, boolean needRetry) {
        DataChangeEventHandlerResult result = failure(message, needRetry);
        result.setErrorCode(errorCode);
        return result;
    }

    /**
     * 设置扩展信息
     *
     * @param key   键
     * @param value 值
     * @return 当前对象
     */
    public DataChangeEventHandlerResult putExtendInfo(String key, Object value) {
        if (extendInfo == null) {
            extendInfo = new java.util.HashMap<>();
        }
        extendInfo.put(key, value);
        return this;
    }

    /**
     * 获取扩展信息
     *
     * @param key 键
     * @return 值
     */
    public Object getExtendInfo(String key) {
        if (extendInfo == null) {
            return null;
        }
        return extendInfo.get(key);
    }
}
